
/**
 * Test utilities for industry flow save/restore functionality
 * This file contains helper functions to test the complete flow schema
 */

import { MarkerType } from '@xyflow/react';

// Define interfaces locally since they're not exported from IndustryFlow
interface FlowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: any;
  style?: any;
}

interface FlowEdge {
  id: string;
  source: string;
  target: string;
  targetHandle?: string;
  sourceHandle?: string;
  label?: string;
  style?: any;
  data?: any;
}

interface NodeFormData {
  formData: any;
  outputs: any[];
  technologies: string[];
  technologyFormData: Record<string, any>;
  completedAt: string;
}

interface IndustryFlowSchema {
  name: string;
  flowType: string;
  createdAt: string;
  updatedAt: string;
  industryId: string;
  reactFlowNodes: FlowNode[];
  reactFlowEdges: FlowEdge[];
  nodeData: Record<string, NodeFormData>;
}

// Sample test data that matches the default template structure
export const createTestFlowSchema = (): IndustryFlowSchema => {
  const testNodes: FlowNode[] = [
    {
      id: '1',
      type: 'custom',
      position: { x: 150, y: 120 },
      data: { label: 'Gas Oil Separation' },
      style: {}
    },
    {
      id: '2',
      type: 'custom',
      position: { x: 500, y: 120 },
      data: { label: 'Condensation' },
      style: {}
    },
    {
      id: 'virtual-input-crude',
      type: 'input',
      position: { x: -50, y: 120 },
      data: { label: 'Crude Oil' },
      style: {
        width: 1,
        height: 1,
        opacity: 0,
        pointerEvents: 'none',
      }
    }
  ];

  const testEdges: FlowEdge[] = [
    {
      id: 'input-crude-1',
      source: 'virtual-input-crude',
      target: '1',
      targetHandle: 'left-target',
      label: 'Crude oil',
      style: {
        stroke: "#10B981",
        strokeWidth: 3,
        opacity: 0.9,
      },
      markerEnd: { type: MarkerType.ArrowClosed, color: "#10B981" },
      data: {
        type: 'input-material',
        material: 'Crude oil',
        sourceActivity: 'Nil',
        technology: 'Nil',
        isInputEdge: true
      }
    },
    {
      id: 'e1-2',
      source: '1',
      sourceHandle: 'right-source',
      target: '2',
      targetHandle: 'left-target',
      label: 'Raw gas',
      style: {
        stroke: '#9b87f5',
        strokeWidth: 2,
        opacity: 0.85,
      },
      markerEnd: { type: MarkerType.ArrowClosed, color: "#9b87f5" }
    }
  ];

  const testNodeFormData: Record<string, NodeFormData> = {
    '1': {
      formData: {
        activity: 'Gas Oil Separation',
        technology: 'Boiler',
        customTechnology: '',
        customActivity: '',
        energyInputs: [],
        emissions: [],
        materialInputs: [
          {
            id: 'material-input-0',
            material: 'Crude oil',
            unit: 'Tonnes',
            cost: '1',
            smc: '1',
            sourceActivity: 'Nil',
            technology: 'Nil'
          }
        ],
        energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
        emission: { source: "", ef: "", unit: "kg" },
        matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
        byproductTechnology: "Boiler",
        byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
        byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
        energyByProducts: [],
        materialByProducts: [],
        financial: { capacity: "1", capacityUnit: "Tonnes/day", capitalCostUnit: "1", omCost: "1" },
        financialEntries: {}
      },
      outputs: [
        {
          id: "output-0",
          targetNode: "2",
          outputTechnology: "Boiler",
          energyOutputs: [],
          matOutputs: [
            {
              id: "material-output-0",
              material: "Raw gas",
              unit: "Tonnes",
              smc: "1",
              final: false,
              connect: "2",
              qty: "1",
              qtyUnit: "Tonnes",
              destinationTechnology: "Boiler"
            }
          ]
        }
      ],
      technologies: ['Boiler'],
      technologyFormData: {
        'Boiler': {
          activity: 'Gas Oil Separation',
          technology: 'Boiler'
        }
      },
      completedAt: new Date().toISOString()
    },
    '2': {
      formData: {
        activity: 'Condensation',
        technology: 'Boiler',
        customTechnology: '',
        customActivity: '',
        energyInputs: [],
        emissions: [],
        materialInputs: [],
        energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
        emission: { source: "", ef: "", unit: "kg" },
        matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
        byproductTechnology: "Boiler",
        byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
        byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
        energyByProducts: [],
        materialByProducts: [],
        financial: { capacity: "1", capacityUnit: "Tonnes/day", capitalCostUnit: "1", omCost: "1" },
        financialEntries: {}
      },
      outputs: [],
      technologies: ['Boiler'],
      technologyFormData: {
        'Boiler': {
          activity: 'Condensation',
          technology: 'Boiler'
        }
      },
      completedAt: new Date().toISOString()
    }
  };

  return {
    name: 'Test Flow Schema',
    flowType: 'inventory',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    reactFlowNodes: testNodes,
    reactFlowEdges: testEdges,
    nodeData: testNodeFormData,
    industryId: 'test-industry'
  };
};

// Validation function to check if a restored flow has all required data
export const validateFlowSchema = (schema: IndustryFlowSchema): boolean => {
  try {
    // Check required fields
    if (!schema.name || !schema.flowType || !schema.createdAt) {
      console.error('Missing required schema fields');
      return false;
    }

    // Check nodes
    if (!Array.isArray(schema.reactFlowNodes) || schema.reactFlowNodes.length === 0) {
      console.error('Invalid or empty nodes array');
      return false;
    }

    // Check edges
    if (!Array.isArray(schema.reactFlowEdges)) {
      console.error('Invalid edges array');
      return false;
    }

    // Check node form data
    if (!schema.nodeData || typeof schema.nodeData !== 'object') {
      console.error('Invalid nodeData');
      return false;
    }

    // Validate each node has required properties
    for (const node of schema.reactFlowNodes) {
      if (!node.id || !node.type || !node.position || !node.data) {
        console.error('Invalid node structure:', node);
        return false;
      }
    }

    // Validate each edge has required properties
    for (const edge of schema.reactFlowEdges) {
      if (!edge.id || !edge.source || !edge.target) {
        console.error('Invalid edge structure:', edge);
        return false;
      }
    }

    console.log('Flow schema validation passed');
    return true;
  } catch (error) {
    console.error('Error validating flow schema:', error);
    return false;
  }
};

// Function to compare two flow schemas for testing
export const compareFlowSchemas = (original: IndustryFlowSchema, restored: IndustryFlowSchema): boolean => {
  try {
    // Compare basic properties
    if (original.name !== restored.name || original.flowType !== restored.flowType) {
      console.error('Basic properties mismatch');
      return false;
    }

    // Compare nodes count
    if (original.reactFlowNodes.length !== restored.reactFlowNodes.length) {
      console.error('Nodes count mismatch');
      return false;
    }

    // Compare edges count
    if (original.reactFlowEdges.length !== restored.reactFlowEdges.length) {
      console.error('Edges count mismatch');
      return false;
    }

    // Compare node form data keys
    const originalKeys = Object.keys(original.nodeData);
    const restoredKeys = Object.keys(restored.nodeData);
    if (originalKeys.length !== restoredKeys.length) {
      console.error('Node form data keys count mismatch');
      return false;
    }

    console.log('Flow schemas comparison passed');
    return true;
  } catch (error) {
    console.error('Error comparing flow schemas:', error);
    return false;
  }
};
